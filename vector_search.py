#!/usr/bin/env python3
"""
Script para busca em vector store do Supabase
Prova de conceito para fazer perguntas e buscar respostas usando embeddings
"""

import os
import sys
from typing import List, Dict, Any
from dotenv import load_dotenv
import openai
from supabase import create_client, Client
import numpy as np

# Carrega variáveis de ambiente
load_dotenv()

class VectorSearch:
    def __init__(self):
        """Inicializa o cliente de busca vetorial"""
        self.setup_openai()
        self.setup_supabase()
        self.setup_config()
    
    def setup_openai(self):
        """Configura cliente OpenAI"""
        api_key = os.getenv('OPENAI_API_KEY')
        if not api_key:
            raise ValueError("OPENAI_API_KEY não encontrada no arquivo .env")
        
        self.openai_client = openai.OpenAI(api_key=api_key)
        print("✓ Cliente OpenAI configurado")
    
    def setup_supabase(self):
        """Configura cliente Supabase"""
        url = os.getenv('SUPABASE_URL')
        key = os.getenv('SUPABASE_KEY')
        
        if not url or not key:
            raise ValueError("SUPABASE_URL e SUPABASE_KEY devem estar no arquivo .env")
        
        self.supabase: Client = create_client(url, key)
        print("✓ Cliente Supabase configurado")
    
    def setup_config(self):
        """Configura parâmetros da tabela"""
        self.table_name = os.getenv('TABLE_NAME', 'documents')
        self.text_column = os.getenv('TEXT_COLUMN', 'content')
        self.embedding_column = os.getenv('EMBEDDING_COLUMN', 'embedding')
        self.metadata_columns = os.getenv('METADATA_COLUMNS', '').split(',')
        self.metadata_columns = [col.strip() for col in self.metadata_columns if col.strip()]
        
        print(f"✓ Configuração: tabela={self.table_name}, texto={self.text_column}, embedding={self.embedding_column}")
    
    def generate_embedding(self, text: str) -> List[float]:
        """Gera embedding para o texto usando OpenAI"""
        try:
            response = self.openai_client.embeddings.create(
                model="text-embedding-3-small",
                input=text
            )
            return response.data[0].embedding
        except Exception as e:
            raise Exception(f"Erro ao gerar embedding: {e}")
    
    def search_similar(self, query: str, limit: int = 5, threshold: float = 0.7) -> List[Dict[str, Any]]:
        """
        Busca documentos similares na vector store
        
        Args:
            query: Pergunta/texto para buscar
            limit: Número máximo de resultados
            threshold: Threshold de similaridade (0-1)
        
        Returns:
            Lista de documentos similares com scores
        """
        print(f"🔍 Buscando por: '{query}'")
        
        # Gera embedding da query
        query_embedding = self.generate_embedding(query)
        
        # Monta colunas para select
        select_columns = [self.text_column, self.embedding_column]
        select_columns.extend(self.metadata_columns)
        select_str = ', '.join(select_columns)
        
        try:
            # Busca usando função de similaridade do Supabase (pgvector)
            response = self.supabase.rpc(
                'match_documents',  # Função que você precisa criar no Supabase
                {
                    'query_embedding': query_embedding,
                    'match_threshold': threshold,
                    'match_count': limit
                }
            ).execute()
            
            results = response.data
            print(f"✓ Encontrados {len(results)} resultados")
            
            return results
            
        except Exception as e:
            print(f"❌ Erro na busca: {e}")
            print("💡 Tentando busca alternativa...")
            
            # Fallback: busca todos e calcula similaridade localmente
            return self._fallback_search(query_embedding, limit, threshold)
    
    def _fallback_search(self, query_embedding: List[float], limit: int, threshold: float) -> List[Dict[str, Any]]:
        """Busca alternativa calculando similaridade localmente"""
        try:
            # Busca todos os documentos
            response = self.supabase.table(self.table_name).select('*').execute()
            documents = response.data
            
            results = []
            query_vec = np.array(query_embedding)
            
            for doc in documents:
                if self.embedding_column in doc and doc[self.embedding_column]:
                    doc_vec = np.array(doc[self.embedding_column])
                    
                    # Calcula similaridade coseno
                    similarity = np.dot(query_vec, doc_vec) / (
                        np.linalg.norm(query_vec) * np.linalg.norm(doc_vec)
                    )
                    
                    if similarity >= threshold:
                        doc['similarity'] = float(similarity)
                        results.append(doc)
            
            # Ordena por similaridade
            results.sort(key=lambda x: x['similarity'], reverse=True)
            
            return results[:limit]
            
        except Exception as e:
            raise Exception(f"Erro na busca alternativa: {e}")
    
    def format_results(self, results: List[Dict[str, Any]]) -> str:
        """Formata resultados para exibição"""
        if not results:
            return "❌ Nenhum resultado encontrado."
        
        output = f"\n📋 Encontrados {len(results)} resultado(s):\n"
        output += "=" * 50 + "\n"
        
        for i, result in enumerate(results, 1):
            similarity = result.get('similarity', 'N/A')
            text = result.get(self.text_column, 'Texto não disponível')
            
            output += f"\n🔸 Resultado {i} (Similaridade: {similarity})\n"
            output += f"📄 Texto: {text[:200]}{'...' if len(text) > 200 else ''}\n"
            
            # Adiciona metadata se disponível
            for col in self.metadata_columns:
                if col in result and result[col]:
                    output += f"📌 {col.title()}: {result[col]}\n"
            
            output += "-" * 30 + "\n"
        
        return output

def main():
    """Função principal - interface de linha de comando"""
    print("🚀 Iniciando Vector Search...")
    
    try:
        # Inicializa o sistema
        searcher = VectorSearch()
        print("\n✅ Sistema inicializado com sucesso!")
        print("💡 Digite 'quit' ou 'exit' para sair\n")
        
        while True:
            # Solicita pergunta do usuário
            query = input("❓ Digite sua pergunta: ").strip()
            
            if query.lower() in ['quit', 'exit', 'sair']:
                print("👋 Até logo!")
                break
            
            if not query:
                print("⚠️  Por favor, digite uma pergunta válida.")
                continue
            
            try:
                # Realiza busca
                results = searcher.search_similar(query, limit=3)
                
                # Exibe resultados
                formatted_output = searcher.format_results(results)
                print(formatted_output)
                
            except Exception as e:
                print(f"❌ Erro durante a busca: {e}")
            
            print("\n" + "="*50 + "\n")
    
    except Exception as e:
        print(f"❌ Erro ao inicializar: {e}")
        print("\n💡 Verifique se:")
        print("   - O arquivo .env está configurado corretamente")
        print("   - As chaves da OpenAI e Supabase estão válidas")
        print("   - A tabela no Supabase existe e tem a estrutura correta")
        sys.exit(1)

if __name__ == "__main__":
    main()
