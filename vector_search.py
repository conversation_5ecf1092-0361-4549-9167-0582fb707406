#!/usr/bin/env python3
"""
Script para busca em vector store do Supabase
Prova de conceito para fazer perguntas e buscar respostas usando embeddings
"""

import os
import sys
from typing import List, Dict, Any
from dotenv import load_dotenv
import openai
from supabase import create_client, Client
import numpy as np

# Carrega variáveis de ambiente
load_dotenv()

class VectorSearch:
    def __init__(self):
        """Inicializa o cliente de busca vetorial"""
        self.setup_openai()
        self.setup_supabase()
        self.setup_config()
    
    def setup_openai(self):
        """Configura cliente OpenAI"""
        api_key = os.getenv('OPENAI_API_KEY')
        if not api_key:
            raise ValueError("OPENAI_API_KEY não encontrada no arquivo .env")
        
        self.openai_client = openai.OpenAI(api_key=api_key)
        print("✓ Cliente OpenAI configurado")
    
    def setup_supabase(self):
        """Configura cliente Supabase"""
        url = os.getenv('SUPABASE_URL')
        key = os.getenv('SUPABASE_KEY')
        
        if not url or not key:
            raise ValueError("SUPABASE_URL e SUPABASE_KEY devem estar no arquivo .env")
        
        self.supabase: Client = create_client(url, key)
        print("✓ Cliente Supabase configurado")
    
    def setup_config(self):
        """Configura parâmetros da tabela"""
        self.table_name = os.getenv('TABLE_NAME', 'documents')
        self.text_column = os.getenv('TEXT_COLUMN', 'content')
        self.embedding_column = os.getenv('EMBEDDING_COLUMN', 'embedding')
        self.metadata_columns = os.getenv('METADATA_COLUMNS', '').split(',')
        self.metadata_columns = [col.strip() for col in self.metadata_columns if col.strip()]
        
        print(f"✓ Configuração: tabela={self.table_name}, texto={self.text_column}, embedding={self.embedding_column}")
    
    def generate_embedding(self, text: str) -> List[float]:
        """Gera embedding para o texto usando OpenAI"""
        try:
            response = self.openai_client.embeddings.create(
                model="text-embedding-3-small",
                input=text
            )
            return response.data[0].embedding
        except Exception as e:
            raise Exception(f"Erro ao gerar embedding: {e}")

    def generate_answer(self, query: str, context_documents: List[Dict[str, Any]]) -> str:
        """Gera resposta usando GPT-4o-mini baseada nos documentos encontrados"""
        if not context_documents:
            return "❌ Não encontrei informações relevantes para responder sua pergunta."

        # Prepara o contexto com os documentos mais relevantes
        context_text = ""
        for i, doc in enumerate(context_documents[:3], 1):  # Usa apenas os 3 mais relevantes
            content = doc.get(self.text_column, '')
            similarity = doc.get('similarity', 0)
            context_text += f"Documento {i} (relevância: {similarity:.3f}):\n{content}\n\n"

        # Prompt para o GPT-4o-mini
        prompt = f"""Com base nos documentos fornecidos, responda à pergunta de forma direta e concisa.

Pergunta: {query}

Contexto dos documentos:
{context_text}

Instruções:
- Responda apenas com base nas informações dos documentos
- Seja direto e objetivo
- Se a informação não estiver nos documentos, diga que não encontrou
- Não invente informações

Resposta:"""

        try:
            response = self.openai_client.chat.completions.create(
                model="gpt-4o-mini",
                messages=[
                    {"role": "system", "content": "Você é um assistente que responde perguntas baseado apenas nos documentos fornecidos."},
                    {"role": "user", "content": prompt}
                ],
                max_tokens=500,
                temperature=0.1
            )

            return response.choices[0].message.content.strip()

        except Exception as e:
            return f"❌ Erro ao gerar resposta: {e}"
    
    def search_similar(self, query: str, limit: int = 5, threshold: float = 0.3) -> List[Dict[str, Any]]:
        """
        Busca documentos similares na vector store
        
        Args:
            query: Pergunta/texto para buscar
            limit: Número máximo de resultados
            threshold: Threshold de similaridade (0-1)
        
        Returns:
            Lista de documentos similares com scores
        """
        print(f"🔍 Buscando por: '{query}'")
        
        # Gera embedding da query
        query_embedding = self.generate_embedding(query)
        
        # Monta colunas para select
        select_columns = [self.text_column, self.embedding_column]
        select_columns.extend(self.metadata_columns)
        select_str = ', '.join(select_columns)
        
        # Busca todos os documentos e calcula similaridade localmente
        return self._search_documents(query_embedding, limit, threshold)
    
    def _search_documents(self, query_embedding: List[float], limit: int, threshold: float) -> List[Dict[str, Any]]:
        """Busca documentos calculando similaridade localmente"""
        try:
            # Busca todos os documentos
            response = self.supabase.table(self.table_name).select('*').execute()
            documents = response.data

            print(f"📊 Total de documentos encontrados: {len(documents)}")

            results = []
            query_vec = np.array(query_embedding, dtype=np.float64)

            for i, doc in enumerate(documents):
                if self.embedding_column in doc and doc[self.embedding_column]:
                    try:
                        # Obtém o embedding do documento
                        embedding_data = doc[self.embedding_column]

                        # Debug: mostra informações do primeiro documento
                        if i == 0:
                            print(f"🔍 Tipo do embedding: {type(embedding_data)}")

                        # Converte string de embedding para lista de floats
                        if isinstance(embedding_data, str):
                            # Remove colchetes e quebras de linha
                            clean_str = embedding_data.strip('[]').replace('\n', '').replace(' ', '')
                            # Divide por vírgula e converte para float
                            embedding_list = [float(x.strip()) for x in clean_str.split(',') if x.strip()]
                        elif isinstance(embedding_data, (list, tuple)):
                            # Se já for lista, garante que são floats
                            embedding_list = [float(x) for x in embedding_data]
                        else:
                            print(f"⚠️  Formato de embedding não reconhecido: {type(embedding_data)}")
                            continue

                        doc_vec = np.array(embedding_list, dtype=np.float64)

                        # Verifica se as dimensões são compatíveis
                        if len(query_vec) != len(doc_vec):
                            print(f"⚠️  Documento {i+1}: Dimensões incompatíveis (query: {len(query_vec)}, doc: {len(doc_vec)})")
                            continue

                        # Calcula similaridade coseno
                        dot_product = np.dot(query_vec, doc_vec)
                        norm_query = np.linalg.norm(query_vec)
                        norm_doc = np.linalg.norm(doc_vec)

                        if norm_query == 0 or norm_doc == 0:
                            continue

                        similarity = dot_product / (norm_query * norm_doc)

                        # Mostra similaridade para os primeiros 5 documentos
                        if i < 5:
                            content_preview = doc.get(self.text_column, '')[:50]
                            print(f"📄 Doc {i+1}: Similaridade {similarity:.3f} - {content_preview}...")

                        if similarity >= threshold:
                            doc['similarity'] = float(similarity)
                            results.append(doc)
                            print(f"✓ Documento {i+1}: ACEITO com similaridade {similarity:.3f}")

                    except Exception as e:
                        print(f"⚠️  Erro ao processar documento {i+1}: {e}")
                        continue

            # Ordena por similaridade
            results.sort(key=lambda x: x['similarity'], reverse=True)

            print(f"🎯 Documentos acima do threshold ({threshold}): {len(results)}")

            return results[:limit]

        except Exception as e:
            raise Exception(f"Erro na busca de documentos: {e}")
    
    def format_results(self, results: List[Dict[str, Any]]) -> str:
        """Formata resultados para exibição"""
        if not results:
            return "❌ Nenhum resultado encontrado."

        output = f"\n📋 Encontrados {len(results)} resultado(s):\n"
        output += "=" * 50 + "\n"

        for i, result in enumerate(results, 1):
            similarity = result.get('similarity', 'N/A')
            text = result.get(self.text_column, 'Texto não disponível')

            output += f"\n🔸 Resultado {i} (Similaridade: {similarity:.3f})\n"
            output += f"📄 Resposta: {text}\n"

            # Adiciona metadata se disponível
            metadata = result.get('metadata', {})
            if metadata and isinstance(metadata, dict):
                for key, value in metadata.items():
                    if value:
                        output += f"📌 {key.title()}: {value}\n"

            output += "-" * 30 + "\n"

        return output

def main():
    """Função principal - interface de linha de comando"""
    print("🚀 Iniciando Vector Search...")
    
    try:
        # Inicializa o sistema
        searcher = VectorSearch()
        print("\n✅ Sistema inicializado com sucesso!")
        print("💡 Digite 'quit' ou 'exit' para sair\n")
        
        while True:
            # Solicita pergunta do usuário
            query = input("❓ Digite sua pergunta: ").strip()
            
            if query.lower() in ['quit', 'exit', 'sair']:
                print("👋 Até logo!")
                break
            
            if not query:
                print("⚠️  Por favor, digite uma pergunta válida.")
                continue
            
            try:
                # Realiza busca com threshold mais baixo
                results = searcher.search_similar(query, limit=3, threshold=0.2)

                # Gera resposta usando GPT-4o-mini
                print("🤖 Gerando resposta...")
                answer = searcher.generate_answer(query, results)

                print(f"\n💬 Resposta:")
                print("=" * 50)
                print(answer)
                print("=" * 50)

                # Opcionalmente, mostra os documentos usados
                print(f"\n📋 Documentos consultados ({len(results)}):")
                for i, result in enumerate(results, 1):
                    similarity = result.get('similarity', 'N/A')
                    text = result.get(searcher.text_column, 'Texto não disponível')
                    print(f"🔸 Doc {i} (Similaridade: {similarity:.3f}): {text[:100]}{'...' if len(text) > 100 else ''}")

            except Exception as e:
                print(f"❌ Erro durante a busca: {e}")
            
            print("\n" + "="*50 + "\n")
    
    except Exception as e:
        print(f"❌ Erro ao inicializar: {e}")
        print("\n💡 Verifique se:")
        print("   - O arquivo .env está configurado corretamente")
        print("   - As chaves da OpenAI e Supabase estão válidas")
        print("   - A tabela no Supabase existe e tem a estrutura correta")
        sys.exit(1)

if __name__ == "__main__":
    main()
