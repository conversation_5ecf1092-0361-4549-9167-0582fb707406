# Vector Search - Prova de Conceito

Script Python para buscar documentos em uma vector store do Supabase usando embeddings da OpenAI.

## 🚀 Setup Rápido

### 1. Instalar dependências
```bash
pip install -r requirements.txt
```

### 2. Configurar variáveis de ambiente
```bash
cp .env.example .env
```

Edite o arquivo `.env` com suas credenciais:
- `OPENAI_API_KEY`: Sua chave da OpenAI
- `SUPABASE_URL`: URL do seu projeto Supabase
- `SUPABASE_KEY`: Service key do Supabase
- `TABLE_NAME`: Nome da tabela que contém os embeddings
- `TEXT_COLUMN`: Nome da coluna que contém o texto
- `EMBEDDING_COLUMN`: Nome da coluna que contém os embeddings

### 3. Configurar Supabase
Execute o SQL em `supabase_setup.sql` no SQL Editor do seu projeto Supabase.

**IMPORTANTE**: Ajuste os nomes das tabelas e colunas no SQL conforme sua estrutura.

### 4. Executar
```bash
python vector_search.py
```

## 📋 Como usar

1. Execute o script
2. Digite sua pergunta quando solicitado
3. O sistema irá:
   - Gerar embedding da sua pergunta usando OpenAI
   - Buscar documentos similares no Supabase
   - Retornar os resultados mais relevantes
4. Digite `quit` ou `exit` para sair

## 🔧 Configuração Avançada

### Parâmetros de busca
No código, você pode ajustar:
- `limit`: Número de resultados (padrão: 3)
- `threshold`: Threshold de similaridade 0-1 (padrão: 0.7)

### Estrutura da tabela
O script assume uma tabela com:
- Coluna de texto (conteúdo)
- Coluna de embedding (vector)
- Colunas de metadata opcionais (título, fonte, etc.)

## 🛠️ Troubleshooting

### Erro "match_documents function not found"
- Execute o SQL em `supabase_setup.sql`
- Verifique se a extensão pgvector está habilitada

### Busca muito lenta
- Crie índices na coluna de embedding
- Ajuste o parâmetro `lists` no índice ivfflat

### Nenhum resultado encontrado
- Verifique se há dados na tabela
- Reduza o threshold de similaridade
- Verifique se os embeddings foram gerados corretamente

## 📝 Próximos passos

- [ ] Adicionar cache de embeddings
- [ ] Implementar diferentes modelos de embedding
- [ ] Adicionar filtros por metadata
- [ ] Interface web simples
