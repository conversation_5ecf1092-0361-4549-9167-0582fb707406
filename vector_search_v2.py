#!/usr/bin/env python3
"""
Script para busca em vector store do Supabase - Versão 2
Prova de conceito para fazer perguntas e buscar respostas usando embeddings
"""

import os
import sys
from typing import List, Dict, Any
from dotenv import load_dotenv
import openai
from supabase import create_client, Client
import numpy as np

# Carrega variáveis de ambiente
load_dotenv()

class VectorSearchV2:
    def __init__(self):
        """Inicializa o cliente de busca vetorial"""
        self.setup_openai()
        self.setup_supabase()
        self.setup_config()
    
    def setup_openai(self):
        """Configura cliente OpenAI"""
        api_key = os.getenv('OPENAI_API_KEY')
        if not api_key:
            raise ValueError("OPENAI_API_KEY não encontrada no arquivo .env")
        
        self.openai_client = openai.OpenAI(api_key=api_key)
        print("✓ Cliente OpenAI configurado")
    
    def setup_supabase(self):
        """Configura cliente Supabase"""
        url = os.getenv('SUPABASE_URL')
        key = os.getenv('SUPABASE_KEY')
        
        if not url or not key:
            raise ValueError("SUPABASE_URL e SUPABASE_KEY devem estar no arquivo .env")
        
        self.supabase: Client = create_client(url, key)
        print("✓ Cliente Supabase configurado")
    
    def setup_config(self):
        """Configura parâmetros da tabela"""
        self.table_name = os.getenv('TABLE_NAME', 'documents_homologacao')
        self.text_column = os.getenv('TEXT_COLUMN', 'content')
        self.embedding_column = os.getenv('EMBEDDING_COLUMN', 'embedding')
        
        print(f"✓ Configuração: tabela={self.table_name}, texto={self.text_column}, embedding={self.embedding_column}")
    
    def generate_embedding(self, text: str) -> List[float]:
        """Gera embedding para o texto usando OpenAI"""
        try:
            response = self.openai_client.embeddings.create(
                model="text-embedding-3-small",
                input=text
            )
            return response.data[0].embedding
        except Exception as e:
            raise Exception(f"Erro ao gerar embedding: {e}")
    
    def search_similar(self, query: str, limit: int = 3, threshold: float = 0.2) -> List[Dict[str, Any]]:
        """
        Busca documentos similares na vector store
        """
        print(f"🔍 Buscando por: '{query}'")
        print(f"📊 Usando threshold: {threshold}")
        
        # Gera embedding da query
        query_embedding = self.generate_embedding(query)
        query_vec = np.array(query_embedding, dtype=np.float64)
        
        # Busca todos os documentos
        response = self.supabase.table(self.table_name).select('*').execute()
        documents = response.data
        
        print(f"📋 Total de documentos encontrados: {len(documents)}")
        
        results = []
        
        for i, doc in enumerate(documents):
            if self.embedding_column in doc and doc[self.embedding_column]:
                try:
                    # Parse do embedding
                    embedding_str = doc[self.embedding_column]
                    clean_str = embedding_str.strip('[]').replace('\n', '').replace(' ', '')
                    embedding_list = [float(x.strip()) for x in clean_str.split(',') if x.strip()]
                    doc_vec = np.array(embedding_list, dtype=np.float64)
                    
                    # Calcula similaridade coseno
                    similarity = np.dot(query_vec, doc_vec) / (np.linalg.norm(query_vec) * np.linalg.norm(doc_vec))
                    
                    # Mostra similaridade para os primeiros 5 documentos
                    if i < 5:
                        content_preview = doc.get(self.text_column, '')[:50]
                        print(f"📄 Doc {i+1}: Similaridade {similarity:.4f} - {content_preview}...")
                    
                    if similarity >= threshold:
                        doc['similarity'] = float(similarity)
                        results.append(doc)
                        print(f"✅ Documento {i+1}: ACEITO com similaridade {similarity:.4f}")
                
                except Exception as e:
                    print(f"⚠️  Erro ao processar documento {i+1}: {e}")
                    continue
        
        # Ordena por similaridade
        results.sort(key=lambda x: x['similarity'], reverse=True)
        
        print(f"🎯 Documentos acima do threshold ({threshold}): {len(results)}")
        
        return results[:limit]
    
    def generate_answer(self, query: str, context_documents: List[Dict[str, Any]]) -> str:
        """Gera resposta usando GPT-4o-mini baseada nos documentos encontrados"""
        if not context_documents:
            return "❌ Não encontrei informações relevantes para responder sua pergunta."
        
        # Prepara o contexto com os documentos mais relevantes
        context_text = ""
        for i, doc in enumerate(context_documents, 1):
            content = doc.get(self.text_column, '')
            similarity = doc.get('similarity', 0)
            context_text += f"Documento {i} (relevância: {similarity:.3f}):\n{content}\n\n"
        
        # Prompt para o GPT-4o-mini
        prompt = f"""Com base nos documentos fornecidos, responda à pergunta de forma direta e concisa.

Pergunta: {query}

Contexto dos documentos:
{context_text}

Instruções:
- Responda apenas com base nas informações dos documentos
- Seja direto e objetivo
- Se a informação não estiver nos documentos, diga que não encontrou
- Não invente informações

Resposta:"""

        try:
            response = self.openai_client.chat.completions.create(
                model="gpt-4o-mini",
                messages=[
                    {"role": "system", "content": "Você é um assistente que responde perguntas baseado apenas nos documentos fornecidos."},
                    {"role": "user", "content": prompt}
                ],
                max_tokens=500,
                temperature=0.1
            )
            
            return response.choices[0].message.content.strip()
            
        except Exception as e:
            return f"❌ Erro ao gerar resposta: {e}"

def main():
    """Função principal - interface de linha de comando"""
    print("🚀 Iniciando Vector Search V2...")
    
    try:
        # Inicializa o sistema
        searcher = VectorSearchV2()
        print("\n✅ Sistema inicializado com sucesso!")
        print("💡 Digite 'quit' ou 'exit' para sair\n")
        
        while True:
            # Solicita pergunta do usuário
            query = input("❓ Digite sua pergunta: ").strip()
            
            if query.lower() in ['quit', 'exit', 'sair']:
                print("👋 Até logo!")
                break
            
            if not query:
                print("⚠️  Por favor, digite uma pergunta válida.")
                continue
            
            try:
                # Realiza busca
                results = searcher.search_similar(query, limit=3, threshold=0.2)
                
                # Gera resposta usando GPT-4o-mini
                print("\n🤖 Gerando resposta...")
                answer = searcher.generate_answer(query, results)
                
                print(f"\n💬 Resposta:")
                print("=" * 50)
                print(answer)
                print("=" * 50)
                
                # Mostra os documentos usados
                if results:
                    print(f"\n📋 Documentos consultados ({len(results)}):")
                    for i, result in enumerate(results, 1):
                        similarity = result.get('similarity', 'N/A')
                        text = result.get(searcher.text_column, 'Texto não disponível')
                        print(f"🔸 Doc {i} (Similaridade: {similarity:.4f}): {text[:100]}{'...' if len(text) > 100 else ''}")
                
            except Exception as e:
                print(f"❌ Erro durante a busca: {e}")
            
            print("\n" + "="*50 + "\n")
    
    except Exception as e:
        print(f"❌ Erro ao inicializar: {e}")
        print("\n💡 Verifique se:")
        print("   - O arquivo .env está configurado corretamente")
        print("   - As chaves da OpenAI e Supabase estão válidas")
        print("   - A tabela no Supabase existe e tem a estrutura correta")
        sys.exit(1)

if __name__ == "__main__":
    main()
