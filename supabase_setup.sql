-- Função para busca de similaridade vetorial no Supabase
-- Execute este SQL no SQL Editor do seu projeto Supabase

-- Habilita a extensão pgvector se ainda não estiver habilitada
CREATE EXTENSION IF NOT EXISTS vector;

-- Função para buscar documentos similares na tabela documents_homologacao
CREATE OR REPLACE FUNCTION match_documents_homologacao (
  query_embedding vector(1536), -- Dimensão do text-embedding-3-small
  match_threshold float,
  match_count int
)
RETURNS TABLE (
  id uuid,
  content text,
  embedding vector,
  similarity float,
  metadata jsonb
)
LANGUAGE sql STABLE
AS $$
  SELECT
    id,
    content,
    embedding,
    1 - (embedding <=> query_embedding) as similarity,
    metadata
  FROM public.documents_homologacao
  WHERE 1 - (embedding <=> query_embedding) > match_threshold
  ORDER BY embedding <=> query_embedding
  LIMIT match_count;
$$;

-- Exemplo de estrutura de tabela (caso você ainda não tenha criado)
-- DESCOMENTE E AJUSTE CONFORME NECESSÁRIO:

/*
CREATE TABLE documents (
  id bigserial PRIMARY KEY,
  content text NOT NULL,
  embedding vector(1536), -- Dimensão do text-embedding-3-small
  title text,
  source text,
  created_at timestamp with time zone DEFAULT now()
);

-- Índice para busca vetorial eficiente
CREATE INDEX ON documents USING ivfflat (embedding vector_cosine_ops)
WITH (lists = 100);
*/
