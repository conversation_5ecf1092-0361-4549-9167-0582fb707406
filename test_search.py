#!/usr/bin/env python3
"""
Teste simples de busca vetorial
"""

import os
from dotenv import load_dotenv
import openai
from supabase import create_client, Client
import numpy as np

# Carrega variáveis de ambiente
load_dotenv()

def test_search():
    # Configura OpenAI
    openai_client = openai.OpenAI(api_key=os.getenv('OPENAI_API_KEY'))
    
    # Configura Supabase
    supabase = create_client(os.getenv('SUPABASE_URL'), os.getenv('SUPABASE_KEY'))
    
    # Pergunta de teste
    query = "quem fundou o grupo index?"
    print(f"🔍 Pergunta: {query}")
    
    # Gera embedding da pergunta
    print("📝 Gerando embedding...")
    response = openai_client.embeddings.create(
        model="text-embedding-3-small",
        input=query
    )
    query_embedding = response.data[0].embedding
    query_vec = np.array(query_embedding, dtype=np.float64)
    print(f"✓ Embedding gerado: {len(query_vec)} dimensões")
    
    # Busca documentos
    print("📊 Buscando documentos...")
    response = supabase.table('documents_homologacao').select('*').execute()
    documents = response.data
    print(f"✓ Encontrados {len(documents)} documentos")
    
    # Calcula similaridades
    similarities = []
    for i, doc in enumerate(documents):
        if doc.get('embedding'):
            try:
                # Parse do embedding
                embedding_str = doc['embedding']
                clean_str = embedding_str.strip('[]').replace('\n', '').replace(' ', '')
                embedding_list = [float(x.strip()) for x in clean_str.split(',') if x.strip()]
                doc_vec = np.array(embedding_list, dtype=np.float64)
                
                # Calcula similaridade
                similarity = np.dot(query_vec, doc_vec) / (np.linalg.norm(query_vec) * np.linalg.norm(doc_vec))
                
                similarities.append({
                    'doc_id': i,
                    'similarity': similarity,
                    'content': doc.get('content', '')[:100]
                })
                
            except Exception as e:
                print(f"Erro no doc {i}: {e}")
    
    # Ordena por similaridade
    similarities.sort(key=lambda x: x['similarity'], reverse=True)
    
    # Mostra top 5
    print(f"\n🎯 Top 5 documentos mais similares:")
    for i, sim in enumerate(similarities[:5]):
        print(f"{i+1}. Similaridade: {sim['similarity']:.4f}")
        print(f"   Conteúdo: {sim['content']}...")
        print()

if __name__ == "__main__":
    test_search()
