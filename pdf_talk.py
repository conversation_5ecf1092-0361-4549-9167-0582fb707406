#!/usr/bin/env python3
"""
PDF Talk - Sistema de perguntas e respostas usando vector store
"""

import os
from dotenv import load_dotenv
import openai
from supabase import create_client, Client
import numpy as np

# Carrega variáveis de ambiente
load_dotenv()

class PDFTalk:
    def __init__(self):
        # Configura OpenAI
        self.openai_client = openai.OpenAI(api_key=os.getenv('OPENAI_API_KEY'))
        
        # Configura Supabase
        self.supabase = create_client(os.getenv('SUPABASE_URL'), os.getenv('SUPABASE_KEY'))
        
        # Configurações
        self.table_name = 'documents_homologacao'
        self.threshold = 0.3  # Threshold de similaridade
        
        print("✅ Sistema inicializado!")
    
    def generate_embedding(self, text: str):
        """Gera embedding usando OpenAI"""
        response = self.openai_client.embeddings.create(
            model="text-embedding-3-small",
            input=text
        )
        return response.data[0].embedding
    
    def search_documents(self, query: str, limit: int = 3):
        """Busca documentos similares"""
        print(f"🔍 Buscando: '{query}'")
        
        # Gera embedding da pergunta
        query_embedding = self.generate_embedding(query)
        query_vec = np.array(query_embedding, dtype=np.float64)
        
        # Busca documentos
        response = self.supabase.table(self.table_name).select('*').execute()
        documents = response.data
        
        # Calcula similaridades
        similarities = []
        for i, doc in enumerate(documents):
            if doc.get('embedding'):
                try:
                    # Parse do embedding
                    embedding_str = doc['embedding']
                    clean_str = embedding_str.strip('[]').replace('\n', '').replace(' ', '')
                    embedding_list = [float(x.strip()) for x in clean_str.split(',') if x.strip()]
                    doc_vec = np.array(embedding_list, dtype=np.float64)
                    
                    # Calcula similaridade
                    similarity = np.dot(query_vec, doc_vec) / (np.linalg.norm(query_vec) * np.linalg.norm(doc_vec))
                    
                    if similarity >= self.threshold:
                        similarities.append({
                            'similarity': similarity,
                            'content': doc.get('content', ''),
                            'metadata': doc.get('metadata', {})
                        })
                        
                except Exception as e:
                    continue
        
        # Ordena por similaridade e retorna os melhores
        similarities.sort(key=lambda x: x['similarity'], reverse=True)
        return similarities[:limit]
    
    def generate_answer(self, query: str, documents):
        """Gera resposta usando GPT-4o-mini"""
        if not documents:
            return "❌ Não encontrei informações relevantes para responder sua pergunta."
        
        # Prepara contexto
        context = ""
        for i, doc in enumerate(documents, 1):
            context += f"Documento {i} (relevância: {doc['similarity']:.3f}):\n"
            context += f"{doc['content']}\n\n"
        
        # Prompt para GPT-4o-mini
        prompt = f"""Com base nos documentos fornecidos, responda à pergunta de forma direta e objetiva.

Pergunta: {query}

Contexto:
{context}

Instruções:
- Responda apenas com base nas informações dos documentos
- Seja direto e conciso
- Se a informação não estiver clara nos documentos, diga que não encontrou
- Não invente informações

Resposta:"""

        try:
            response = self.openai_client.chat.completions.create(
                model="gpt-4o-mini",
                messages=[
                    {"role": "system", "content": "Você é um assistente que responde perguntas baseado apenas nos documentos fornecidos."},
                    {"role": "user", "content": prompt}
                ],
                max_tokens=500,
                temperature=0.1
            )
            
            return response.choices[0].message.content.strip()
            
        except Exception as e:
            return f"❌ Erro ao gerar resposta: {e}"
    
    def ask(self, question: str):
        """Faz uma pergunta e retorna a resposta"""
        # Busca documentos relevantes
        documents = self.search_documents(question)
        
        print(f"📊 Encontrados {len(documents)} documentos relevantes")
        
        # Gera resposta
        answer = self.generate_answer(question, documents)
        
        return answer, documents

def main():
    """Interface de linha de comando"""
    print("🚀 PDF Talk - Sistema de Perguntas e Respostas")
    print("💡 Digite 'quit' para sair\n")
    
    try:
        pdf_talk = PDFTalk()
        
        while True:
            question = input("❓ Sua pergunta: ").strip()
            
            if question.lower() in ['quit', 'exit', 'sair']:
                print("👋 Até logo!")
                break
            
            if not question:
                print("⚠️  Digite uma pergunta válida.")
                continue
            
            try:
                # Faz a pergunta
                answer, documents = pdf_talk.ask(question)
                
                # Mostra resposta
                print(f"\n💬 Resposta:")
                print("=" * 60)
                print(answer)
                print("=" * 60)
                
                # Mostra documentos consultados
                if documents:
                    print(f"\n📋 Documentos consultados:")
                    for i, doc in enumerate(documents, 1):
                        similarity = doc['similarity']
                        content = doc['content'][:100]
                        print(f"🔸 Doc {i} (Similaridade: {similarity:.3f}): {content}...")
                
                print("\n" + "="*60 + "\n")
                
            except Exception as e:
                print(f"❌ Erro: {e}")
    
    except Exception as e:
        print(f"❌ Erro ao inicializar: {e}")

if __name__ == "__main__":
    main()
