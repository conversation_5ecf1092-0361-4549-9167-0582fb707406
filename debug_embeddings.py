#!/usr/bin/env python3
"""
Script para debugar os embeddings no Supabase
"""

import os
from dotenv import load_dotenv
from supabase import create_client, Client

# Carrega variáveis de ambiente
load_dotenv()

def debug_embeddings():
    """Debug dos embeddings armazenados"""
    
    # Configura Supabase
    url = os.getenv('SUPABASE_URL')
    key = os.getenv('SUPABASE_KEY')
    table_name = os.getenv('TABLE_NAME', 'documents_homologacao')
    
    supabase: Client = create_client(url, key)
    
    print("🔍 Analisando embeddings no Supabase...")
    
    # Busca alguns documentos para análise
    response = supabase.table(table_name).select('*').limit(3).execute()
    documents = response.data
    
    print(f"📊 Total de documentos encontrados: {len(documents)}")
    
    for i, doc in enumerate(documents):
        print(f"\n--- Documento {i+1} ---")
        print(f"ID: {doc.get('id', 'N/A')}")
        
        # Analisa o conteúdo
        content = doc.get('content', '')
        print(f"Conteúdo: {content[:100]}{'...' if len(content) > 100 else ''}")
        
        # Analisa o embedding
        embedding = doc.get('embedding', None)
        if embedding:
            print(f"Tipo do embedding: {type(embedding)}")
            
            if isinstance(embedding, str):
                print(f"Embedding (string): {embedding[:100]}...")
            elif isinstance(embedding, list):
                print(f"Embedding (lista): {len(embedding)} elementos")
                if len(embedding) > 0:
                    print(f"Primeiro elemento: {type(embedding[0])} = {embedding[0]}")
                    print(f"Último elemento: {type(embedding[-1])} = {embedding[-1]}")
            else:
                print(f"Embedding (outro): {str(embedding)[:100]}...")
        else:
            print("❌ Sem embedding")
        
        # Analisa metadata
        metadata = doc.get('metadata', {})
        if metadata:
            print(f"Metadata: {metadata}")

if __name__ == "__main__":
    debug_embeddings()
